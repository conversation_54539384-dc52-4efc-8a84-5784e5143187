import base64
from openai import OpenAI

# Path to your local image file
image_path = "eq1.png"

# Read and encode the image as base64
with open(image_path, "rb") as image_file:
    encoded_string = base64.b64encode(image_file.read()).decode("utf-8")

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="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",
)

completion = client.chat.completions.create(
    extra_body={},
    model="mistralai/mistral-small-3.1-24b-instruct:free",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "give me latex "
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encoded_string}"
                    }
                }
            ]
        }
    ]
)

print(completion.choices[0].message.content)
