import base64
import os
from google import genai
from google.genai import types

# Path to your local image file
image_path = "eq2.png"

# Read and encode the image as base64
with open(image_path, "rb") as image_file:
    encoded_string = base64.b64encode(image_file.read()).decode("utf-8")

client = genai.Client(
    api_key="AIzaSyCFe4kZGOLzv7sG5n1Wgzdxa8BsuF2gOiU",
)

model = "gemini-2.5-flash"

contents = [
    types.Content(
        role="user",
        parts=[
            types.Part.from_text(text="give me latex"),
            types.Part(
                inline_data=types.Blob(
                    mime_type="image/png",
                    data=base64.b64decode(encoded_string)
                )
            ),
        ],
    ),
]

generate_content_config = types.GenerateContentConfig(
    thinking_config=types.ThinkingConfig(
        thinking_budget=-1,
    ),
    response_mime_type="text/plain",
)

response = client.models.generate_content(
    model=model,
    contents=contents,
    config=generate_content_config,
)

print(response.text)
